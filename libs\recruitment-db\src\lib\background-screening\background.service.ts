import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { BackgroundModel } from "./background.model";
import { Op, Sequelize } from "sequelize";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { User } from "../users/users.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { ChangeBackgroundDto, FilterJobsBackgroundDto } from "./background.dto";
import { BGVStatus } from "./background.enum";
import { FilterCandidatesBackgroundDto, BackgroundDto } from "./background.dto"
import { Round } from "../rounds/round.model";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";
import { HttpService } from "@nestjs/axios";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { roundCompletedSignal, roundRejectedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { CompaniesService } from "../companies/companies.service";
import { EmailService } from "@microservices/email";
import { Company } from "../companies/companies.model";
@Injectable()
export class BackgroundService {
  constructor(
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(BackgroundModel)
    private backgroundRepository: typeof BackgroundModel,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe,
    private fileService: FileService,
    private httpService: HttpService,
    private companyService: CompaniesService,
    private emailService: EmailService
  ) { }

  async getAllJobsCompanyWise(dto: FilterJobsBackgroundDto, companyId: number) {
    const where: any = { companyId,isAssessment:false,status:'publish' };
    const locationWhere: any = {};
    const order = [];
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }
    if (dto.locations) {
      if (!Array.isArray(dto.locations)) {
        locationWhere.id = { [Op.eq]: dto.locations };
      } else {
        locationWhere.id = { [Op.in]: dto.locations };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    const verifiedInBGVQuery = `(SELECT COUNT(*) FROM "background-screening" AS bs WHERE bs."jobId" = "Jobs"."id" AND bs."BGV_status" ILIKE '%${BGVStatus.VERIFIED}%')`;
    const rejectedInBGVQuery = `(SELECT COUNT(*) FROM "background-screening" AS bs WHERE bs."jobId" = "Jobs"."id" AND bs."BGV_status" ILIKE '%${BGVStatus.REJECTED}%')`;
    const sentForBGVQuery = `(SELECT COUNT(*) FROM "background-screening" AS bs WHERE bs."jobId" = "Jobs"."id" AND bs."BGV_status" NOT LIKE '%${BGVStatus.IN_PROGRESS}%')`;
    const NotSentForBGVQuery = `(SELECT COUNT(*) FROM "background-screening" AS bs WHERE bs."jobId" = "Jobs"."id" AND bs."BGV_status" ILIKE '%${BGVStatus.NOT_INITIATED}%')`;

    return await this.jobRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      order,
      attributes: [
        "id",
        "title",
        "numberOpenings",
        [Sequelize.literal(verifiedInBGVQuery), "VerifiedCandidatesCount"],
        [Sequelize.literal(rejectedInBGVQuery), "rejectedCandidatesCount"],
        [Sequelize.literal(sentForBGVQuery), "sentCandidatesForBGVCount"],
        [Sequelize.literal(NotSentForBGVQuery), "NotSentCandidatesForBGVCount"],
      ],

      include: [
        {
          model: Location,
          where: locationWhere,
        },
      ],
    });
  }

  async getAllCandidatesJobWise(
    dto: FilterCandidatesBackgroundDto,
    companyId: number,
    jobId: number
  ) {
    const userWhere: any = {};
    const backgroundWhere: any = { companyId,jobId };
    let hasUserFilter = false;
    let hasBackgroundFilter = false;
    let where: any = { jobId }
    const order = [];
    if (dto.search) {
      userWhere[Op.or] = [
        { firstname: { [Op.iLike]: `%${dto.search}%` } },
        { lastname: { [Op.iLike]: `%${dto.search}%` } },
      ];
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["createdAt", "DESC"]);
    }

    if (dto.vendor) {
      backgroundWhere.vendor = {
        [Op.eq]: dto.vendor,
      };
    }

    if (dto.dateFrom && dto.dateTo) {
      backgroundWhere.createdAt = {
        [Op.between]: [new Date(dto.dateFrom), new Date(dto.dateTo)],
      };
    } else if (dto.dateFrom || dto.dateTo) {
      if (dto.dateFrom) {
        backgroundWhere.createdAt = {
          [Op.gte]: new Date(dto.dateFrom),
        };
      }
      if (dto.dateTo) {
        backgroundWhere.createdAt = {
          [Op.lte]: new Date(dto.dateTo),
        };
      }
    }

    if (dto.search) {
      hasUserFilter = true;
    }

    if (dto.vendor || dto.status || dto.dateFrom || dto.dateTo) {
      hasBackgroundFilter = true;
    }
    if (dto.status) {
    
      const statusArray = dto.status.split(",");
      const filteredStatus = statusArray.filter((item) => item !== "Not Initiated");
    
      if (filteredStatus.length > 0) {
        backgroundWhere.BGV_status = { [Op.in]: filteredStatus };
        hasBackgroundFilter = true;
      }
    
      if (statusArray.includes("Not Initiated")) {
        where.backgroundOrderId = { [Op.is]: null };
        hasBackgroundFilter=false
      }
    }

    return await this.subscribeRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      include: [
        {
          model: User,
          where: userWhere,
          required: hasUserFilter,

          include: [
            {
              model: BackgroundModel,
              where: backgroundWhere,
              required: hasBackgroundFilter,
            },
          ],
        },
        {
          model: Round
        }
      ],
      attributes: { exclude: ["summary"] },
    });
  }

  async updateById(
    backgroundId: number,
    dto: BackgroundDto,
    companyId: number
  ) {
    try {
      const data = await this.backgroundRepository.findOne({
        where: {
          id: backgroundId,
          companyId,
        },
      });

      Object.assign(data, {
        ...dto,
        userId: data.userId,
        jobId: data.jobId,
        companyId,
      });

      return data.save();
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async createBackgroundOrder(dto: BackgroundDto, companyId: number) {
    try {
      const data = await this.backgroundRepository.findOne({
        where: {
          jobId: dto.jobId,
          userId: dto.userId,
          companyId,
          BGV_status: {
            [Op.not]: BGVStatus.REJECTED,
          },
        },
        attributes: ["id"],
      });

      if (data) {
        throw new HttpException(
          "The Screening of this Candidate is balance or in progress",
          HttpStatus.CONFLICT
        );
      } else {

        const backgroundOrder = await this.backgroundRepository.create({
          ...dto,
          BGV_status: dto.status,
          companyId,
        });

        await this.subscribeRepository.update(
          { backgroundOrderId: backgroundOrder.id },
          {
            where: {
              jobId: dto.jobId,
              userId: dto.userId,
            },
          }
        );

        if (backgroundOrder.BGV_status === BGVStatus.VERIFIED || backgroundOrder.BGV_status === BGVStatus.REJECTED){
          await this.sendSignalToTemporal(backgroundOrder.BGV_status,backgroundOrder.jobId,backgroundOrder.userId )
        }
        return backgroundOrder;
      }
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getAllBackgroundCompanyWise(companyId: number) {
    try {

      return await this.backgroundRepository.findAndCountAll({
        where: {
          companyId
        },
        include: [{
          model: User,
          attributes: ["id", "firstname", "lastname", "avatar"]
        },
        {
          model: Jobs,
          attributes: ["id", "title", "companyId"],
          where:{
            isAssessment:false
          }
        }],
      })
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async changeBackgroundStatus(companyId, dto: ChangeBackgroundDto) {
    const { userId, jobId, status } = dto
    if (!userId) {
      throw new BadRequestException("User Id is not provided.")
    }
    if (!jobId) {
      throw new BadRequestException("Job Id is not provided.")
    }
    const background = await this.backgroundRepository.findOne({ where: { userId, companyId } })
    if (background && status) {
      background.BGV_status = status
      await background.save()
      await this.sendSignalToTemporal(status,background.jobId,background.userId )
    }

    return background;
  }

  async getBackgroundById(backgroundId, companyId) {
    return await this.backgroundRepository.findOne({ where: { id: backgroundId, companyId }, include: [{ model: Jobs,where:{isAssessment:false}, attributes: ["id", "title", "status"] }] })
  }

  async getBackgroundByUserId(userId, companyId,jobId=null) {
    return await this.backgroundRepository.findOne({ where: { userId, companyId,
      ...(!!jobId ? {jobId}:'')
     }, include: [{ model: Jobs,where:{isAssessment:false}, attributes: ["id", "title", "status"] },Company] })
  }

  async uploadBackgroundReport({ body, userId, companyId }) {
    try {

      const user = await this.userRepository.findOne({ where: { id: userId } })
      if (user && body) {
        const Key = `company/${user.authId}/${userId}_${companyId}_background_report`;

        await this.fileService.uploadOnS3(
          {
            Bucket: process.env.UPLOADSPRIVATE_NAME,
            Key,
            Body: body,
            ContentType: "application/pdf",
            ACL: 'private'
          }
        );
      }
    } catch (err) {
      Logger.log("Error uploading background report.")
      throw new InternalServerErrorException("Error uploading background report." + err)
    }
  }

  async getBackgroundReport({ userId, companyId }) {
    if (!userId) {
      throw new BadRequestException("UserId is not provided");
    }

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (user && companyId) {
      const Key = `company/${user.authId}/${userId}_${companyId}_background_report`;

      const pdfUrl = await this.fileService.getFromS3({
        Bucket: process.env.UPLOADSPRIVATE_NAME,
        Key,
      });

      if (pdfUrl) {
        const pdfResponse = await this.httpService.get(pdfUrl, { responseType: 'arraybuffer' }).toPromise();
        if (pdfResponse) {
          return pdfResponse.data;
        }
      }
    }
  }

  async sendReviewMailToCompany(companyId, userId,href,type:'Background'|'Drug') {
    if (!companyId) {
      throw new BadRequestException('Company ID is not Provided.')
    }
    if (!userId) {
      throw new BadRequestException('User ID is not Provided.')
    }
    const company = await this.companyService.getCompany(companyId)
    const candidate = await this.userRepository.findOne({ where: { id: userId } })
    if (company && candidate) {
      const body = `
          <h2>Review ${type} Screening on Universal</h2>
          <div style="margin-bottom: 34px">
              <p>Dear ${company.firstname + " " + company.lastname},</p>
              <p>We are pleased to inform you that the ${type} screening report for candidate <strong>${candidate.firstname} ${candidate.lastname}</strong> is now available for review.</p>
              <p>You can access the report via the Universal platform. Kindly ensure that you mark the report as reviewed to make it visible on the uRecruits platform.</p>
              <p>To review the ${type} screening for your candidate, please click the link below:</p>
          </div>
          <a href="${href}"
              style="
                  text-decoration: none;
                  color: inherit;
                  display: block;
                  width: 100%;
                  height: 100%;
              ">
              <button style="
                  font-size: 14px;
                  font-weight: 900;
                  line-height: 100%;
                  color: white;
                  padding: 16px 28px;
                  border: none;
                  cursor: pointer;
                  background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
                  border-radius: 4px;
              ">View ${type} Screening Report</button>
          </a>`;
            await this.emailService.sendRecruitmentActivityService('trialEnds.html', {
              body,
              userId: company?.ownerId,
            }, company.email, `${type} Screening Report is Ready For Review`,{
              link:href,
              notificationTitle:`${type} Screening Report is Ready For Review`,
              notificationMsg:`The ${type} screening report for ${candidate.firstname} ${candidate.lastname} is available for review on the Universal platform. Please review and mark it as reviewed.`
            }
          )
        }
  }


  async sendSignalToTemporal(status,jobId,userId){
    try {
      const url = `${process.env.RECRUITMENT_API_URI}/api/temporal-workflow?jobId=${jobId}&id=${userId}`;
      let response
      try {
        const res = await this.httpService.get(url).toPromise();
        response = res?.data;
      } catch (error) {
        console.error("Error fetching data:", error);
      }

      const client = await connectToTemporal()
      if (client && response?.workflowid) {
        const handle = client.getHandle(response?.workflowid)
        if (status === BGVStatus.VERIFIED) {
          await handle.signal(
            roundCompletedSignal,
            `Background Screening is completed`
          );
        } else if (status === BGVStatus.REJECTED) {
          await handle.signal(
            roundRejectedSignal,
            `Background Screening is Rejected`
          );
        }
      }
    } catch (error) {
      Logger.log("ERROR while signaling to temporal", error)
    }
  }
}
