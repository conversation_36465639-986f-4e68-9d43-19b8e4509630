import { Injectable } from '@nestjs/common';
import { StateGraph, START, END, Annotation, MemorySaver } from '@langchain/langgraph';
import {
  createJob<PERSON>reationNode,
  createWorkflowCreationNode,
  createAssessmentNode,
  createIntentClassifierNode,
} from './langgraph.nodes';
import {
  JobCreationChatbotService,
  WorkflowChatbotService,
} from '@microservices/recruitment-db';

@Injectable()
export class LangGraphService {
  private checkpointer: MemorySaver;

  constructor(
    private readonly jobCreationService: JobCreationChatbotService,
    private readonly workflowCreationService: WorkflowChatbotService,
  ) {
    this.checkpointer = new MemorySaver(); // Use MemorySaver for testing
  }

  async run(input: {
    sessionId: string;
    userInput: string;
    userId: number;
    companyId: number;
    authHeader?: string;
    userPermissions?: string[];
  }) {
    const { sessionId, userInput, userId, companyId, authHeader, userPermissions } = input;

    const State = Annotation.Root({
      sessionId: Annotation<string>(),
      userInput: Annotation<string>(),
      userId: Annotation<number>(),
      companyId: Annotation<number>(),
      authHeader: Annotation<string | undefined>(),
      userPermissions: Annotation<string[] | undefined>(),
      intent: Annotation<string | undefined>(),
      previousIntent: Annotation<string | undefined>(),
      conversationStage: Annotation<string | undefined>(),
      chatResponse: Annotation<string | undefined>(),
      formattedJobDetails: Annotation<any>(),
      formattedWorkflowDetails: Annotation<any>(),
      formattedAssessmentDetails: Annotation<any>(),
      isChatReset: Annotation<boolean | undefined>(),
      resetTimestamp: Annotation<string | undefined>(),
      workflowCreationContext: Annotation<{
        isFromWorkflow: boolean;
        assessmentType?: 'domain' | 'take-home' | 'live-coding';
        workflowSteps?: string[];
        workflowTitle?: string;
        returnToWorkflow?: boolean;
        createdAssessmentId?: number;
        createdAssessmentName?: string;
        needsConfiguration?: boolean;
        timeDuration?: number;
        deadline?: number;
        assessmentData?: any;
      } | undefined>(),
    });

    const graphBuilder = new StateGraph(State);

    // Nodes
    const intentClassifier = createIntentClassifierNode();
    const jobNode = createJobCreationNode(this.jobCreationService);
    const workflowNode = createWorkflowCreationNode(this.workflowCreationService);
    const assessmentNode = createAssessmentNode('http://localhost:3000/api/assessment-chatbot/converse');

    // Register nodes
    graphBuilder.addNode('intent-classifier', intentClassifier);
    graphBuilder.addNode('job-agent', jobNode);
    graphBuilder.addNode('workflow-agent', workflowNode);
    graphBuilder.addNode('assessment-agent', assessmentNode);

    // Edges
    graphBuilder.addEdge(START as any, 'intent-classifier' as any);
    graphBuilder.addConditionalEdges('intent-classifier' as any, (state) => {
      const isJobWorkflowAssignment = (
        state.conversationStage?.includes('job') ||
        state.conversationStage === 'workflow_selection' ||
        (state.intent === 'workflow' && state.conversationStage === 'job_creation')
      );

      if (isJobWorkflowAssignment) {
        return 'job-agent';
      }

      switch (state.intent) {
        case 'workflow':
          return 'workflow-agent';
        case 'assessment':
          return 'assessment-agent';
        case 'job':
        default:
          return 'job-agent';
      }
    });

    // Add conditional edges for workflow-assessment bidirectional flow
    graphBuilder.addConditionalEdges('workflow-agent' as any, (state) => {

      // Check if workflow agent wants to transfer to assessment creation
      if (state.conversationStage === 'assessment_creation' && state.intent === 'assessment') {
        return 'assessment-agent';
      }

      // All other cases should END - no loops back to workflow-agent
      return END;
    });

    // Add conditional edges for assessment-workflow return flow
    graphBuilder.addConditionalEdges('assessment-agent' as any, (state) => {

      // Check if assessment agent wants to return to workflow creation
      if (state.conversationStage === 'workflow_creation' && state.intent === 'workflow') {
        return 'workflow-agent';
      }
      // Check if assessment agent wants to handle workflow assignment
      if (state.conversationStage === 'workflow_assignment' && state.intent === 'workflow') {
        return 'workflow-agent';
      }
      // Check for configuration collection stages that should go to workflow
      if (state.conversationStage?.startsWith('collect_') && state.intent === 'workflow') {
        return 'workflow-agent';
      }
      return END;
    });

    // Job agent still goes directly to END
    graphBuilder.addEdge('job-agent' as any, END);

    const graph = graphBuilder.compile({
      checkpointer: this.checkpointer
    });


    const result = await graph.invoke(
      {
        sessionId,
        userInput,
        userId,
        companyId,
        authHeader,
        userPermissions,
      },
      { configurable: { thread_id: sessionId } },
    );

    return result;
  }
}