import { HttpService } from '@nestjs/axios';
import { connectToTemporal } from "../workflow.provider";
import { Logger,Injectable } from "@nestjs/common";

@Injectable()
export class TemporalClientService {
  constructor(private readonly httpService: HttpService) { }

  async getHandle(jobId, candidateId) {
    const url = `${process.env.RECRUITMENT_API_URI}/api/temporal-workflow?jobId=${jobId}&id=${candidateId}`;
    let response
    try {
      const res = await this.httpService.get(url).toPromise();
      response = res?.data;
    } catch (error) {
      console.error("Error fetching data:", error);
    }

    const client = await connectToTemporal();
  
    if (client && response) {
      return client.getHandle(response?.workflowid);

    } else {
      Logger.log(
        "Getting issue to connect the client in domain result"
      );
    }
  }
}


