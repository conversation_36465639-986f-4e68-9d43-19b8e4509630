 

# Microservices

This project was generated using [Nx](https://nx.dev).

🔎 **Smart, Fast and Extensible Build System**

## Setting up the project
- Run `yarn` to install the dependencies
- Run  `yarn build` to build the project
- Replace your .env.example with the .env and fill the environment variables.
- Run  `yarn start:all` to run the project

Pre-requirements:
- Docker:
  - Docker Desktop - https://www.docker.com/products/docker-desktop
  - Homebrew `brew install docker`
  - Chocolatey `choco install docker-cli`

- Redis. To setup Redis install it locally, use docker image, or brew package
  - Install it from official website: https://redis.io/
  - Just brew it `brew install redis`. Some additional informations: https://gist.github.com/tomysmile/1b8a321e7c58499ef9f9441b2faa0aa8
    - How to see all services that we have in brew `brew services`
    - Hot to start or stop service in brew `brew services start redis` || `brew services stop redis`

Guide how to run project:
- Set up the databse: 
  - First of all change name of .env.example to .env
  - Then run `npm run db:setup` || `yarn db:setup`
  - Then run `npm run redis` || `yarn redis`
  - Then run `npm run temporal` || `yarn temporal`

- Start the project:
  - For running the project you would need running docker image of database and also redis instance.
  - For run all applications `npm run start:all` || `yarn start:all`
  - For run application separately:
    - Run only assessment API `npm run start` || `yarn start`
    - Run only pre-recruitment API `npm run start:pre` || `yarn start:pre`

- Start the temporal worker:
  - For running the temporal worker with updated code you would need run `npm run build:workflow` || `yarn build:workflow`.
  - When yo change anything in workflow.ts file the hit above command

## Adding capabilities to your workspace

Nx supports many plugins which add capabilities for developing different types of applications and different tools.

These capabilities include generating applications, libraries, etc as well as the devtools to test, and build projects as well.

Below are our core plugins:

- [React](https://reactjs.org)
  - `npm install --save-dev @nrwl/react`
- Web (no framework frontends)
  - `npm install --save-dev @nrwl/web`
- [Angular](https://angular.io)
  - `npm install --save-dev @nrwl/angular`
- [Nest](https://nestjs.com)
  - `npm install --save-dev @nrwl/nest`
- [Express](https://expressjs.com)
  - `npm install --save-dev @nrwl/express`
- [Node](https://nodejs.org)
  - `npm install --save-dev @nrwl/node`

There are also many [community plugins](https://nx.dev/community) you could add.

## Generate an application

Run `nx g @nrwl/react:app my-app` to generate an application.

> You can use any of the plugins above to generate applications as well.

When using Nx, you can create multiple applications and libraries in the same workspace.

## Generate a library

Run `nx g @nrwl/react:lib my-lib` to generate a library.

> You can also use any of the plugins above to generate libraries as well.

Libraries are shareable across libraries and applications. They can be imported from `@microservices/mylib`.

## Development server

Run `nx serve my-app` for a dev server. Navigate to http://localhost:4200/. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `nx g @nrwl/react:component my-component --project=my-app` to generate a new component.

## Build

Run `nx build my-app` to build the project. The build artifacts will be stored in the `dist/` directory. Use the `--prod` flag for a production build.

## Running unit tests

Run `nx test my-app` to execute the unit tests via [Jest](https://jestjs.io).

Run `nx affected:test` to execute the unit tests affected by a change.

## Running end-to-end tests

Run `ng e2e my-app` to execute the end-to-end tests via [Cypress](https://www.cypress.io).

Run `nx affected:e2e` to execute the end-to-end tests affected by a change.

## Understand your workspace

Run `nx dep-graph` to see a diagram of the dependencies of your projects.

## Further help

Visit the [Nx Documentation](https://nx.dev) to learn more.



## ☁ Nx Cloud

### Distributed Computation Caching & Distributed Task Execution

<p style="text-align: center;"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-cloud-card.png"></p>

Nx Cloud pairs with Nx in order to enable you to build and test code more rapidly, by up to 10 times. Even teams that are new to Nx can connect to Nx Cloud and start saving time instantly.

Teams using Nx gain the advantage of building full-stack applications with their preferred framework alongside Nx’s advanced code generation and project dependency graph, plus a unified experience for both frontend and backend developers.

Visit [Nx Cloud](https://nx.app/) to learn more.
