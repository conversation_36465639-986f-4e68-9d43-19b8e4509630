import { JobCreationChatbotService, WorkflowChatbotService } from '@microservices/recruitment-db';
import axios from 'axios';
import { ChatOpenAI } from '@langchain/openai';
import { SystemMessage, HumanMessage, AIMessage } from 'langchain/schema';
import { QdrantClient } from '@qdrant/js-client-rest';

// Permission checking utility function
function checkUserPermissions(userPermissions: string[] = [], requiredPermissions: string[]): boolean {

  if (!userPermissions || userPermissions.length === 0) {
    return false;
  }

  // Check if user has any of the required permissions (OR logic)
  const hasPermission = requiredPermissions.some(permission => userPermissions.includes(permission));

  return hasPermission;
}

type SharedAgentState = {
  sessionId: string;
  userInput: string;
  userId: number;
  companyId: number;
  authHeader?: string;
  userPermissions?: string[];
  intent?: string;
  previousIntent?: string;
  conversationStage?: string;
  chatResponse?: string;
  formattedJobDetails?: any;
  formattedWorkflowDetails?: any;
  formattedAssessmentDetails?: any;
  isChatReset?: boolean;
  resetTimestamp?: string;

  workflowCreationContext?: {
    isFromWorkflow: boolean;
    assessmentType?: 'domain' | 'take-home' | 'live-coding';
    workflowSteps?: string[];
    workflowTitle?: string;
    returnToWorkflow?: boolean;
    createdAssessmentId?: number;
    createdAssessmentName?: string;
    needsConfiguration?: boolean;
    timeDuration?: number;
    deadline?: number;
    assessmentData?: any;
  };
};

// Helper function to handle assessment configuration collection
async function handleAssessmentConfiguration(
  state: SharedAgentState,
  workflowService: WorkflowChatbotService
): Promise<SharedAgentState> {
  const userInput = state.userInput.trim();
  const context = state.workflowCreationContext;

  if (!context) {
    return {
      ...state,
      chatResponse: 'Error: Missing workflow context. Please start over.',
      conversationStage: 'workflow_creation',
    };
  }

  try {
    if (state.conversationStage === 'collect_live_duration') {
      const duration = parseInt(userInput, 10);
      if (isNaN(duration) || duration < 1 || duration > 8) {
        return {
          ...state,
          chatResponse: 'Please enter a valid duration between 1 and 8 hours.',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, duration, null);

      return {
        ...state,
        chatResponse: `✅ Perfect! Live coding assessment "${context.createdAssessmentName}" has been configured with ${duration} hour(s) duration and added to your workflow.\n\n🔄 Continuing with workflow creation. Would you like to continue?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          timeDuration: duration,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

    if (state.conversationStage === 'collect_domain_deadline') {
      const deadline = parseInt(userInput, 10);
      if (isNaN(deadline) || deadline < 1 || deadline > 168) { // Max 1 week
        return {
          ...state,
          chatResponse: 'Please enter a valid deadline between 1 and 168 hours (1 week).',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, null, deadline);

      return {
        ...state,
        chatResponse: `✅ Perfect! Domain assessment "${context.createdAssessmentName}" has been configured with ${deadline} hour(s) deadline and added to your workflow.\n\n🔄 Continuing with workflow creation. Would you like to continue?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          deadline: deadline,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

    if (state.conversationStage === 'collect_takehome_deadline') {
      const deadlineDays = parseInt(userInput, 10);
      if (isNaN(deadlineDays) || deadlineDays < 1 || deadlineDays > 7) {
        return {
          ...state,
          chatResponse: 'Please enter a valid deadline between 1 and 7 days.',
        };
      }

      return {
        ...state,
        chatResponse: `Great! Deadline set to ${deadlineDays} day(s). Now, please specify the time duration for this take-home assessment in hours (e.g., 2, 4, 6):`,
        conversationStage: 'collect_takehome_duration',
        workflowCreationContext: {
          ...context,
          deadline: deadlineDays * 24, // Convert days to hours
        },
      };
    }

    if (state.conversationStage === 'collect_takehome_duration') {
      const duration = parseInt(userInput, 10);
      if (isNaN(duration) || duration < 1 || duration > 24) {
        return {
          ...state,
          chatResponse: 'Please enter a valid duration between 1 and 24 hours.',
        };
      }

      // Prepare assessment data for workflow creation
      const assessmentData = prepareAssessmentDataForWorkflow(state, duration, context.deadline);

      return {
        ...state,
        chatResponse: `Perfect! Assessment "${context.createdAssessmentName}" has been configured with ${context.deadline! / 24} day(s) deadline and ${duration} hour(s) duration and added to your workflow.\n\n🔄 Continuing with workflow creation. What other steps would you like to include?`,
        conversationStage: 'workflow_creation',
        workflowCreationContext: {
          ...context,
          timeDuration: duration,
          needsConfiguration: false,
          assessmentData: assessmentData,
        },
      };
    }

  } catch (error) {
    console.error('[handleAssessmentConfiguration] Error:', error);
    return {
      ...state,
      chatResponse: 'Something went wrong while configuring the assessment. Please try again.',
      conversationStage: 'workflow_creation',
    };
  }

  return state;
}

// Helper function to prepare assessment data for workflow creation
function prepareAssessmentDataForWorkflow(
  state: SharedAgentState,
  duration: number | null,
  deadline: number | null
): any {
  const context = state.workflowCreationContext;
  if (!context) return {};

  const assessmentData: any = {};

  if (context.assessmentType === 'live-coding') {
    assessmentData.liveCodingId = context.createdAssessmentId;
    assessmentData.liveCodingTime = duration;
    assessmentData.liveCodingQuestionType = 'live-task';
  } else if (context.assessmentType === 'domain') {
    assessmentData.domainId = context.createdAssessmentId;
    assessmentData.domainDeadline = deadline;
  } else if (context.assessmentType === 'take-home') {
    assessmentData.takeHomeTaskId = context.createdAssessmentId;
    assessmentData.takeHomeTaskDeadline = deadline;
    assessmentData.takeHomeTaskTime = duration;
    assessmentData.takeHomeTaskQuestionType = 'take-home-task';
  }

  return assessmentData;
}

export function createJobCreationNode(jobService: JobCreationChatbotService) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create a job and validate permissions
      const isJobCreationAttempt = (
        state.intent === 'job' ||
        state.userInput.toLowerCase().includes('create job') ||
        state.userInput.toLowerCase().includes('post job') ||
        state.userInput.toLowerCase().includes('new job') ||
        state.userInput.toLowerCase().includes('go lang') ||
        state.userInput.toLowerCase().includes('golang') ||
        state.userInput.toLowerCase().includes('developer') ||
        state.conversationStage === 'job_creation' ||
        (state.conversationStage === 'initial' && state.intent === 'job')
      );

      if (isJobCreationAttempt && !checkUserPermissions(state.userPermissions, ['job-post:add'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create jobs. Please contact your administrator to grant you the "job-post:add" permission.',
          conversationStage: 'permission_denied',
        };
      }

      const result = await jobService.startConversation(
        { sessionId: state.sessionId, userInput: state.userInput },
        state.companyId,
        state.userId
      );
    
      let conversationStage = 'job_creation';
    
      if (result.chatResponse?.includes('Here are your available workflows:') ||
          result.chatResponse?.includes('Which workflow would you like to assign') ||
          result.chatResponse?.includes('available workflows for assignment') ||
          result.chatResponse?.includes('workflow(s) available for assignment') ||
          result.chatResponse?.includes('just type the workflow name')) {
        conversationStage = 'workflow_selection';
      }
      else if (result.chatResponse?.includes('has been assigned to the job') ||
               result.chatResponse?.includes('workflow assigned successfully') ||
               result.chatResponse?.includes('Workflow assignment completed')) {
        conversationStage = 'job_creation';
      }
      else if (result.chatResponse?.includes('Workflow') &&
               (result.chatResponse?.includes('not found') ||
                result.chatResponse?.includes('Please check'))) {
        conversationStage = 'workflow_selection';
      }
      else {
        conversationStage = state.conversationStage === 'workflow_selection' ? 'workflow_selection' : 'job_creation';
      }

      return {
        ...state,
        chatResponse: result.chatResponse,
        formattedJobDetails: result.formattedJobDetails,
        conversationStage,
      };
    } catch (error) {
      console.error('Error in job-agent node:', error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your request.',
        formattedJobDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

export function createWorkflowCreationNode(workflowService: WorkflowChatbotService) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create a workflow and validate permissions
      const isWorkflowCreationAttempt = (
        state.intent === 'workflow' ||
        state.userInput.toLowerCase().includes('create workflow') ||
        state.userInput.toLowerCase().includes('new workflow') ||
        state.conversationStage === 'workflow_creation' ||
        state.conversationStage?.startsWith('collect_') ||
        (state.conversationStage === 'initial' && state.intent === 'workflow')
      );

      if (isWorkflowCreationAttempt && !checkUserPermissions(state.userPermissions, ['job-post:add', 'job-post:edit'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create workflows. Please contact your administrator to grant you either "job-post:add" or "job-post:edit" permission.',
          conversationStage: 'permission_denied',
        };
      }

      // Handle assessment configuration collection stages
      if (state.conversationStage?.startsWith('collect_')) {
        return handleAssessmentConfiguration(state, workflowService);
      }

      // Prepare the request body with assessment data if available
      const requestBody: any = {
        sessionId: state.sessionId,
        userInput: state.userInput
      };

      // Add assessment data to the request if available
      if (state.workflowCreationContext?.assessmentData) {
        requestBody.assessmentData = state.workflowCreationContext.assessmentData;
      }

      const result = await workflowService.startConversation(
        requestBody,
        state.companyId,
        state.userId,
        state.authHeader
      ) as { chatResponse: string; formattedWorkflowDetails: any };

      // Enhanced detection for assessment creation transfer
      const isAssessmentTransfer = result.chatResponse?.includes('You\'ll be transferred to the assessment creation system') ||
                                   result.chatResponse?.includes('I\'ll help you create a new') ||
                                   result.chatResponse?.includes('assessment creation system') ||
                                   result.chatResponse?.includes('Please specify \'') ||
                                   result.chatResponse?.includes('to begin creation') ||
                                   result.chatResponse?.includes('create a domain assessment') ||
                                   result.chatResponse?.includes('create a take-home assessment') ||
                                   result.chatResponse?.includes('create a live coding assessment') ||
                                   result.chatResponse?.includes('Let\'s create your assessment') ||
                                   result.chatResponse?.includes('assessment creation process') ||
                                   result.chatResponse?.includes('I\'ll help you create a live coding assessment') ||
                                   result.chatResponse?.includes('I\'ll help you create a take-home assessment') ||
                                   result.chatResponse?.includes('I\'ll help you create a domain assessment');

      if (isAssessmentTransfer) {
        let assessmentType: 'domain' | 'take-home' | 'live-coding' = 'domain';

        // More robust assessment type detection
        if (result.chatResponse.includes('take-home') || result.chatResponse.includes('take home')) {
          assessmentType = 'take-home';
        } else if (result.chatResponse.includes('live coding') || result.chatResponse.includes('live-coding') ||
                   result.chatResponse.includes('live task') || result.chatResponse.includes('coding assessment') ||
                   result.chatResponse.includes('Live Task/Coding Assessment')) {
          assessmentType = 'live-coding';
        } else if (result.chatResponse.includes('domain') || result.chatResponse.includes('knowledge') ||
                   result.chatResponse.includes('Functional/Domain Assessment')) {
          assessmentType = 'domain';
        }


        return {
          ...state,
          chatResponse: result.chatResponse,
          formattedWorkflowDetails: result.formattedWorkflowDetails,
          conversationStage: 'assessment_creation',
          intent: 'assessment',
          workflowCreationContext: {
            isFromWorkflow: true,
            assessmentType,
            returnToWorkflow: true,
            workflowTitle: result.formattedWorkflowDetails?.title || 'Current Workflow',
            workflowSteps: result.formattedWorkflowDetails?.steps || [],
          },
        };
      }

      return {
        ...state,
        chatResponse: result.chatResponse,
        formattedWorkflowDetails: result.formattedWorkflowDetails,
        conversationStage: state.conversationStage === 'workflow_selection' ? 'job_creation' : 'workflow_creation',
      };
    } catch (error) {
      console.error('Error in workflow-agent node:', error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your request.',
        formattedWorkflowDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

export function createAssessmentNode(apiBaseUrl: string) {
  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      // Check if user is trying to create an assessment and validate permissions
      const isAssessmentCreationAttempt = (
        state.intent === 'assessment' ||
        state.userInput.toLowerCase().includes('create assessment') ||
        state.userInput.toLowerCase().includes('new assessment') ||
        state.userInput.toLowerCase().includes('live coding') ||
        state.userInput.toLowerCase().includes('take-home') ||
        state.userInput.toLowerCase().includes('domain assessment') ||
        state.conversationStage === 'assessment_creation' ||
        (state.conversationStage === 'initial' && state.intent === 'assessment')
      );

      if (isAssessmentCreationAttempt && !checkUserPermissions(state.userPermissions, ['assessment:edit'])) {
        return {
          ...state,
          chatResponse: '**Permission Denied**: You do not have the required permissions to create assessments. Please contact your administrator to grant you the "assessment:edit" permission.',
          conversationStage: 'permission_denied',
        };
      }

      const assessmentApiUrl = process.env.ASSESSMENT_API_URI || 'http://localhost:5481';
      const apiUrl = `${assessmentApiUrl}/api/assessment-chatbot/converse`;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: state.authHeader,
      };

      // Map our workflowCreationContext to the format expected by assessment service
      const workflowContext = state.workflowCreationContext ? {
        isFromWorkflow: state.workflowCreationContext.isFromWorkflow,
        assessmentType: state.workflowCreationContext.assessmentType,
        workflowSteps: state.workflowCreationContext.workflowSteps,
        workflowTitle: state.workflowCreationContext.workflowTitle,
        returnToWorkflow: state.workflowCreationContext.returnToWorkflow,
      } : undefined;

      const body = {
        sessionId: state.sessionId,
        userInput: state.userInput,
        companyId: state.companyId,
        userId: state.userId,
        workflowContext: workflowContext,
      };


      const response = await axios.post(apiUrl, body, { headers });

      const result = response.data as {
        response: string;
        formattedAssessmentDetails: any;
        assessmentCreated?: boolean;
        assessmentId?: number;
        assessmentName?: string;
        data?: any;
        completed?: boolean;
        workflowAssignment?: any;
      };


      const isAssessmentCompleted = result.assessmentCreated ||
                                   result.response?.includes('assessment has been created') ||
                                   result.response?.includes('successfully created') ||
                                   result.response?.includes('Assessment submitted successfully');

      const isWorkflowAssignmentResponse = result.response?.includes('Would you like to assign this assessment to your workflow?') ||
                                          result.response?.includes('assign this assessment to the workflow') ||
                                          result.response?.includes('add this assessment to your workflow');

      // Check if this is a workflow context assessment that should return directly
      const isFromWorkflowContext = state.workflowCreationContext?.isFromWorkflow &&
                                   state.workflowCreationContext?.returnToWorkflow &&
                                   isAssessmentCompleted;


      if (isWorkflowAssignmentResponse && !isFromWorkflowContext) {
        return {
          ...state,
          chatResponse: result.response,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: 'workflow_assignment',
          intent: 'workflow',
        };
      }

      const isWorkflowAssignmentComplete = (result.response?.includes('assigned to your workflow') ||
                                          result.response?.includes('Returning to workflow creation') ||
                                          result.response?.includes('not assigned to the workflow') ||
                                          result.response?.includes('assessment has been assigned')) &&
                                          (result.completed || result.assessmentCreated);

      if (isWorkflowAssignmentComplete) {
        return {
          ...state,
          chatResponse: result.response,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: 'workflow_creation',
          intent: 'workflow',
        };
      }

      // Enhanced logic for returning to workflow after assessment creation
      if (isFromWorkflowContext) {

        const workflowTitle = state.workflowCreationContext.workflowTitle || 'your workflow';
        const assessmentName = result.assessmentName || result.data?.name || 'New Assessment';
        const assessmentType = state.workflowCreationContext.assessmentType;

        // Try to extract assessment ID from different possible locations
        let assessmentId = result.assessmentId || result.data?.id;
        if (!assessmentId && result.data?.jsonOutput) {
          // For live coding, the ID might be in the created record
        }


        // Determine what additional parameters to collect based on assessment type
        let nextPrompt = '';
        let nextStage = 'workflow_creation';

        if (assessmentType === 'live-coding') {
          nextPrompt = `✅ Great! The live coding assessment "${assessmentName}" has been created successfully.\n\n⏱️ Now, please specify the time duration for this live coding assessment in hours (e.g., 1, 2, 3):`;
          nextStage = 'collect_live_duration';
        } else if (assessmentType === 'domain') {
          nextPrompt = `✅ Great! The domain assessment "${assessmentName}" has been created successfully.\n\n📅 Now, please specify the deadline for this domain assessment in hours (e.g., 24, 48, 72):`;
          nextStage = 'collect_domain_deadline';
        } else if (assessmentType === 'take-home') {
          nextPrompt = `✅ Great! The take-home assessment "${assessmentName}" has been created successfully.\n\n📅 Now, please specify the deadline for this take-home assessment in days (e.g., 1, 2, 3):`;
          nextStage = 'collect_takehome_deadline';
        }


        return {
          ...state,
          chatResponse: nextPrompt,
          formattedAssessmentDetails: result.formattedAssessmentDetails,
          conversationStage: nextStage,
          intent: 'workflow',
          workflowCreationContext: {
            ...state.workflowCreationContext,
            returnToWorkflow: false,
            createdAssessmentId: assessmentId,
            createdAssessmentName: assessmentName,
            needsConfiguration: true,
          },
        };
      }

      return {
        ...state,
        chatResponse: result.response,
        formattedAssessmentDetails: result.formattedAssessmentDetails,
        conversationStage: 'assessment_creation',
      };
    } catch (error) {
      console.error('Error in assessment-agent node:', error.response?.data || error);
      return {
        ...state,
        chatResponse: 'Something went wrong while processing your assessment request.',
        formattedAssessmentDetails: null,
        conversationStage: 'initial',
      };
    }
  };
}

function detectChatReset(userInput: string): boolean {
  const resetKeywords = [
    'reset chat', 'clear chat', 'restart chat', 'new chat', 'start over',
    'reset conversation', 'clear conversation', 'restart conversation',
    'exit', 'quit', 'end chat', 'start fresh', 'begin again',
    'reset session', 'clear session', 'new session'
  ];

  const input = userInput.toLowerCase().trim();
  return resetKeywords.some(keyword => input.includes(keyword)) ||
         /^(reset|clear|restart|exit|quit)$/i.test(input);
}

async function storeResetMarker(qdrantClient: QdrantClient, sessionId: string, userId: number, companyId: number): Promise<string> {
  const resetTimestamp = new Date().toISOString();
  const resetId = `${sessionId}_reset_${Date.now()}`;

  try {
    const resetVector = new Array(1536).fill(0);
    resetVector[0] = 1;

    await qdrantClient.upsert('conversations', {
      wait: true,
      points: [{
        id: resetId,
        vector: resetVector,
        payload: {
          sessionId,
          userId,
          companyId,
          flowType: 'chat_reset',
          resetTimestamp,
          isResetMarker: true,
          messages: [{ role: 'system', content: `Chat reset at ${resetTimestamp}` }]
        }
      }]
    });

    return resetTimestamp;
  } catch (error) {
    console.error('[ChatReset] Error storing reset marker:', error);
    return resetTimestamp;
  }
}



export function createIntentClassifierNode() {
  const chatModel = new ChatOpenAI({
    modelName: process.env.OPEN_AI_MODEL || 'gpt-4o-mini',
    temperature: 0,
    openAIApiKey: process.env.OPEN_AI_SECRET_KEY,
  });

  const systemPrompt = `You are an advanced intent classifier for a recruitment system. Your primary task is to analyze the ENTIRE conversation history and the current user input to accurately determine intent changes and conversation flow.

CRITICAL ANALYSIS REQUIREMENTS:
1. **Chat Reset Detection**: If the user wants to reset/clear/restart the chat, this should be handled specially
2. **Intent Change Detection**: If the user mentions a NEW task (like "create workflow", "create assessment", "create job") while in the middle of another task, this is a NEW INTENT, not a continuation.
3. **Context vs New Request**: Distinguish between:
   - Continuing current task: "yes", "no", "add more details", "change the title"
   - Starting new task: "create workflow", "make assessment", "post new job", "I want to create..."
4. **Conversation History Analysis**: Look at the full conversation to understand what the user was previously doing vs what they're asking for now.

AVAILABLE INTENTS:
- workflow: For workflow-related tasks (creating, editing, assigning workflows)
- job: For job-related tasks (creating, editing, posting jobs)
- assessment: For assessment-related tasks (creating assessments, live tasks, take-home tasks, domain assessments)
- reset: For chat reset/clear/restart commands
- custom: For any other specific intents

DETAILED INTENT ACTIONS AND KEYWORDS:

WORKFLOW INTENT:
Actions: workflow creation, workflow assignment, workflow customization, workflow templates
Keywords: "create workflow", "new workflow", "workflow creation", "make workflow", "build workflow", "workflow for", "custom workflow", "assign workflow", "attach workflow", "workflow template", "screening workflow", "interview workflow", "assessment workflow", "hiring workflow"
Stages: workflow_creation, workflow_selection, workflow_assignment
Context: Creating recruitment workflows, assigning workflows to jobs, customizing workflow steps

JOB INTENT:
Actions: job posting, job creation, job editing, job publishing, job requirements
Keywords: "create job", "post job", "new job", "job posting", "job creation", "make job", "add job", "job description", "job requirements", "job title", "salary", "location", "experience", "skills", "publish job", "save job"
Stages: job_creation, job_editing, job_publishing
Context: Creating job postings, defining job requirements, setting job details, publishing jobs

ASSESSMENT INTENT:
Actions: assessment creation, live coding, take-home tasks, domain assessments, question generation
Keywords: "create assessment", "new assessment", "assessment creation", "live task", "take-home task", "coding assessment", "make assessment", "domain assessment", "technical assessment", "coding questions", "programming test", "algorithm test", "live coding", "coding challenge"
Sub-types:
  - Take-home: "take-home task", "take home assessment", "coding assignment", "programming assignment"
  - Live-task: "live coding", "live assessment", "real-time coding", "live programming test"
  - Domain: "domain assessment", "knowledge test", "theoretical assessment", "conceptual questions"
Stages: assessment_creation, question_generation, assessment_configuration
Context: Creating coding assessments, generating questions, configuring test parameters

RESET INTENT:
Actions: chat reset, conversation clear, session restart
Keywords: "reset chat", "clear chat", "restart chat", "new chat", "start over", "reset conversation", "clear conversation", "exit", "quit", "end chat", "start fresh", "begin again", "reset session", "clear session", "new session"
Context: User wants to start fresh conversation without losing data

RESPONSE FORMAT:
{
  "intent": "string",
  "subIntent": "string",
  "action": "string",
  "isConfirmation": boolean,
  "isRejection": boolean,
  "isNewIntent": boolean,
  "isChatReset": boolean,
  "conversationStage": "string",
  "confidence": number,
  "reasoning": "string",
  "detectedKeywords": ["string"]
}

CLASSIFICATION RULES:
1. **Chat Reset Detection**: If user input contains reset/clear/restart keywords, mark as chat reset with high confidence
2. **New Intent Detection**: If user input contains creation keywords for a different domain than current conversation, mark as new intent
3. **Sub-Intent Classification**: For assessments, identify specific type (take-home, live-task, domain)
4. **Action Detection**: Identify specific action within the intent (creation, editing, assignment, etc.)
5. **Confirmation/Rejection**: Simple yes/no responses, confirmations, or rejections of current flow
6. **Context Continuation**: Responses that add details or modify current task
7. **Assessment Context Awareness**: When in assessment_creation stage, numeric inputs (1-300) are configuration values (duration, questions, scores), NOT feedback
8. **Programming Language Recognition**: Language names (Python, Java, Ruby, etc.) in assessment context are language selections, not job requirements
9. **Confidence Scoring**: High confidence (0.8+) for clear intent keywords, lower for ambiguous input
10. **Keyword Extraction**: List the specific keywords that led to the classification
11. **Reasoning**: Always provide detailed reasoning for your classification decision

DETAILED EXAMPLES:

CHAT RESET EXAMPLES:
- Input: "reset chat" → intent: "reset", isChatReset: true, confidence: 0.95
- Input: "clear conversation" → intent: "reset", isChatReset: true, confidence: 0.95
- Input: "start over" → intent: "reset", isChatReset: true, confidence: 0.9

NEW INTENT DETECTION EXAMPLES:
- Current: Job creation (step 3), Input: "create workflow" → intent: "workflow", isNewIntent: true, action: "creation", confidence: 0.9
- Current: Workflow creation, Input: "create assessment" → intent: "assessment", isNewIntent: true, action: "creation", confidence: 0.9
- Current: Assessment creation, Input: "create job instead" → intent: "job", isNewIntent: true, action: "creation", confidence: 0.85

ASSESSMENT SUB-INTENT EXAMPLES:
- Input: "create take-home assessment" → intent: "assessment", subIntent: "take-home", action: "creation", confidence: 0.9
- Input: "live coding test" → intent: "assessment", subIntent: "live-task", action: "creation", confidence: 0.85
- Input: "domain knowledge assessment" → intent: "assessment", subIntent: "domain", action: "creation", confidence: 0.85

WORKFLOW SPECIFIC EXAMPLES:
- Input: "create screening workflow" → intent: "workflow", action: "creation", confidence: 0.9
- Input: "assign workflow to job" → intent: "workflow", action: "assignment", confidence: 0.85
- Input: "customize interview workflow" → intent: "workflow", action: "customization", confidence: 0.8

JOB SPECIFIC EXAMPLES:
- Input: "post new job" → intent: "job", action: "creation", confidence: 0.9
- Input: "edit job description" → intent: "job", action: "editing", confidence: 0.85
- Input: "publish job posting" → intent: "job", action: "publishing", confidence: 0.85

CONTINUATION EXAMPLES:
- Current: Job creation, Input: "change the title to Senior Developer" → intent: "job", isNewIntent: false, action: "editing", confidence: 0.7
- Current: Assessment creation, Input: "add more questions" → intent: "assessment", isNewIntent: false, action: "modification", confidence: 0.75
- Current: Assessment creation, Input: "60" → intent: "assessment", isNewIntent: false, action: "configuration", confidence: 0.9 (numeric input for duration/score)
- Current: Assessment creation, Input: "Python" → intent: "assessment", isNewIntent: false, action: "configuration", confidence: 0.9 (language selection)
- Current: Assessment creation, Input: "5" → intent: "assessment", isNewIntent: false, action: "configuration", confidence: 0.9 (number of questions)
- Current: Assessment creation, Input: "What is the capital of France" → intent: "assessment", isNewIntent: false, action: "configuration", confidence: 0.9 (question text input)
- Current: Assessment creation, Input: "multiple choice" → intent: "assessment", isNewIntent: false, action: "configuration", confidence: 0.9 (question type)
- Current: Workflow creation, Input: "add interview step" → intent: "workflow", isNewIntent: false, action: "modification", confidence: 0.75

CONFIRMATION/REJECTION EXAMPLES:
- Current: Any creation flow, Input: "yes, proceed" → isConfirmation: true, confidence: 0.95
- Current: Any creation flow, Input: "no, cancel" → isRejection: true, confidence: 0.95
- Current: Question generation, Input: "regenerate questions" → isRejection: true, confidence: 0.8

CRITICAL CONTEXT RULES:
- If conversationStage contains "assessment_creation" and input is numeric (1-300), it's assessment configuration, NOT feedback
- If conversationStage contains "assessment_creation" and input is a programming language name, it's language selection, NOT job creation
- If conversationStage contains "assessment_creation" and input is question text or assessment details, it's assessment configuration, NOT job creation
- If conversationStage contains "assessment_creation", maintain intent: "assessment" with high confidence unless explicitly changing tasks
- Question text like "What is the capital of France" in assessment context is assessment configuration, NOT job posting
- **CRITICAL: "new" in assessment context**: If conversationStage is "assessment_creation" and user says "new", this means "create new questions" for the assessment, NOT "create new job" - maintain intent: "assessment"
- **CRITICAL: "existing" in assessment context**: If conversationStage is "assessment_creation" and user says "existing", this means "use existing questions" for the assessment, NOT job-related - maintain intent: "assessment"
- **WORKFLOW ASSIGNMENT IN JOB CONTEXT**: If conversation history shows "assign a workflow" or "available workflows" and current intent is "job", workflow names are job workflow assignments, NOT new workflow creation
- If user types workflow names after being shown available workflows in job context, maintain intent: "job" for workflow assignment within job creation`

  return async (state: SharedAgentState): Promise<SharedAgentState> => {
    try {
      const qdrantClient = new QdrantClient({
        url: process.env.QDRANT_DATABASE_URL || 'http://localhost:6333',
        apiKey: process.env.QDRANT_API_KEY,
      });

      const qdrantResponse = await qdrantClient.scroll('conversations', {
        filter: {
          must: [
            { key: 'sessionId', match: { value: state.sessionId } }
          ],
        },
        limit: 50,
      });

      let conversationHistory: { role: string; content: string }[] = [];
      let currentConversationStage = state.conversationStage || 'initial';
      let currentIntent = state.intent || 'unknown';
      let lastResetTimestamp: string | null = null;

      if (qdrantResponse.points && qdrantResponse.points.length > 0) {
        const resetMarkers = qdrantResponse.points
          .filter(point => point.payload?.isResetMarker === true)
          .sort((a, b) => new Date(b.payload?.resetTimestamp as string).getTime() - new Date(a.payload?.resetTimestamp as string).getTime());

        if (resetMarkers.length > 0) {
          lastResetTimestamp = resetMarkers[0].payload?.resetTimestamp as string;
        }

        const conversationPoints = qdrantResponse.points
          .filter(point => point.payload?.flowType !== 'chat_reset' && point.payload?.isResetMarker !== true)
          .filter(point => {
            if (!lastResetTimestamp) return true;
            const pointTimestamp = point.payload?.timestamp as string;
            return pointTimestamp && new Date(pointTimestamp) > new Date(lastResetTimestamp);
          });

        if (conversationPoints.length > 0) {
          const payload = conversationPoints[0].payload as any;
          if (payload && Array.isArray(payload.messages)) {
            conversationHistory = payload.messages.filter((msg: any) => {
              if (!lastResetTimestamp) return true;
              return !msg.timestamp || new Date(msg.timestamp) > new Date(lastResetTimestamp);
            }).slice(-15);
          }
        }
      }

      conversationHistory.push({ role: 'user', content: state.userInput });

      const contextSummary = `
CURRENT CONVERSATION CONTEXT:
- Current Intent: ${currentIntent}
- Current Stage: ${currentConversationStage}
- Previous Intent: ${state.previousIntent || 'none'}
- Session ID: ${state.sessionId}

CONVERSATION HISTORY (last ${conversationHistory.length} messages):
${conversationHistory.map((msg, idx) => `${idx + 1}. ${msg.role}: ${msg.content}`).join('\n')}

ANALYSIS TASK:
Analyze the conversation history and current user input to determine:
1. Is this a NEW intent (user wants to start a different task)?
2. Is this a continuation of the current task?
3. Is this a confirmation/rejection of the current flow?
4. What should be the next conversation stage?`;

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(contextSummary),
        ...conversationHistory.slice(-10).map(msg =>
          msg.role === 'user' ? new HumanMessage(msg.content) : new AIMessage(msg.content)
        ),
      ];

      const llmResponse = await chatModel.invoke(messages);

      const content = llmResponse.content;
      if (typeof content === 'string') {
        try {
          const parsed = JSON.parse(content);

          if (state.conversationStage === 'assessment_creation') {
            const isAssessmentContinuation = (
              ['new', 'existing', 'yes', 'no', 'y', 'n'].includes(state.userInput.toLowerCase().trim()) ||
              /^\d+$/.test(state.userInput.trim()) ||
              state.userInput.toLowerCase().includes('question') ||
              state.userInput.toLowerCase().includes('assessment') ||
              state.userInput.toLowerCase().includes('domain') ||
              /^\d+\s*(minutes?|mins?|hours?|hrs?|%|percent)?\s*$/i.test(state.userInput.trim()) ||
              state.userInput.toLowerCase().includes('what is') ||
              state.userInput.toLowerCase().includes('how to') ||
              state.userInput.toLowerCase().includes('explain') ||
              state.userInput.toLowerCase().includes('define') ||
              state.userInput.toLowerCase().includes('multiple choice') ||
              state.userInput.toLowerCase().includes('true false') ||
              state.userInput.toLowerCase().includes('short answer') ||
              conversationHistory.some(msg =>
                msg.content.toLowerCase().includes('would you like to use existing domain questions or create new questions') ||
                msg.content.toLowerCase().includes('what industry is this assessment for') ||
                msg.content.toLowerCase().includes('what department is this assessment for')
              )
            );

            if (isAssessmentContinuation || !parsed.isNewIntent) {
              return {
                ...state,
                intent: 'assessment',
                previousIntent: 'assessment',
                conversationStage: 'assessment_creation',
              };
            }
          }

          if (state.conversationStage === 'workflow_creation' && !parsed.isNewIntent) {
            return {
              ...state,
              intent: 'workflow',
              previousIntent: 'workflow',
              conversationStage: 'workflow_creation',
            };
          }

          if (state.conversationStage === 'workflow_assignment' && !parsed.isNewIntent) {
            return {
              ...state,
              intent: 'workflow',
              previousIntent: 'workflow',
              conversationStage: 'workflow_assignment',
            };
          }

          // Handle assessment configuration collection stages - these should stay in workflow
          if (state.conversationStage?.startsWith('collect_') && !parsed.isNewIntent) {
            return {
              ...state,
              intent: 'workflow',
              previousIntent: 'workflow',
              conversationStage: state.conversationStage, // Keep the current collection stage
            };
          }

          if (parsed.isChatReset === true || parsed.intent === 'reset') {
            const resetTimestamp = await storeResetMarker(qdrantClient, state.sessionId, state.userId, state.companyId);

            return {
              ...state,
              intent: 'reset',
              previousIntent: undefined,
              conversationStage: 'initial',
              isChatReset: true,
              resetTimestamp,
              chatResponse: 'Chat has been reset. How can I help you today?',
            };
          }

          if (parsed.isNewIntent === true) {
            const newIntent = parsed.intent?.toLowerCase().trim() || 'unknown';
            const validIntents = ['job', 'workflow', 'assessment', 'custom'];

            const isInJobWorkflowAssignment = (
              (state.conversationStage === 'job_creation' && state.intent === 'job') &&
              conversationHistory.some(msg =>
                msg.content.toLowerCase().includes('would you like to assign a workflow') ||
                msg.content.toLowerCase().includes('which workflow would you like to assign') ||
                msg.content.toLowerCase().includes('available workflows') ||
                msg.content.toLowerCase().includes('here are your available workflows') ||
                msg.content.toLowerCase().includes('just type the workflow name')
              ) &&
              (parsed.reasoning &&
               parsed.reasoning.toLowerCase().includes('job') &&
               (parsed.reasoning.toLowerCase().includes('workflow') || parsed.reasoning.toLowerCase().includes('assign')))
            );

            const finalIntent = isInJobWorkflowAssignment && newIntent === 'workflow'
              ? 'job'
              : (validIntents.includes(newIntent) ? newIntent : 'assessment');

            const subIntent = parsed.subIntent || '';
            const action = parsed.action || 'creation';
            const detectedKeywords = parsed.detectedKeywords || [];

            let conversationStage = 'initial';
            if (finalIntent === 'workflow') {
              if (action === 'assignment' || state.userInput.toLowerCase().includes('attach workflow') ||
                  state.userInput.toLowerCase().includes('assign workflow')) {
                conversationStage = 'workflow_selection';
              } else {
                conversationStage = 'workflow_creation';
              }
            } else if (finalIntent === 'job') {
              if (action === 'assignment' || state.userInput.toLowerCase().includes('attach workflow') ||
                  state.userInput.toLowerCase().includes('assign workflow') || isInJobWorkflowAssignment) {
                conversationStage = 'job_creation';
              } else if (action === 'editing') {
                conversationStage = 'job_editing';
              } else if (action === 'publishing') {
                conversationStage = 'job_publishing';
              } else {
                conversationStage = 'job_creation';
              }
            } else if (finalIntent === 'assessment') {
              conversationStage = 'assessment_creation';
            }

            return {
              ...state,
              intent: finalIntent,
              previousIntent: finalIntent,
              conversationStage,
            };
          }

          if (parsed.isConfirmation || parsed.isRejection) {
            let conversationStage = state.conversationStage;

            if (parsed.isConfirmation) {
              switch (conversationStage) {
                case 'workflow_selection':
                  conversationStage = 'workflow_creation';
                  break;
                case 'workflow_creation':
                  conversationStage = 'job_creation';
                  break;
                case 'assessment_creation':
                  break;
                default:
                  conversationStage = 'initial';
              }
            } else {
              conversationStage = 'initial';
            }

            return {
              ...state,
              intent: state.previousIntent || 'job',
              previousIntent: state.previousIntent || 'job',
              conversationStage,
            };
          }

          const intent = parsed.intent?.toLowerCase().trim() || 'unknown';
          const validIntents = ['job', 'workflow', 'assessment', 'custom'];

          const subIntent = parsed.subIntent || '';
          const action = parsed.action || '';
          const detectedKeywords = parsed.detectedKeywords || [];

          const isInJobWorkflowAssignment = (
            (state.conversationStage === 'job_creation' && state.intent === 'job') &&
            conversationHistory.some(msg =>
              msg.content.toLowerCase().includes('would you like to assign a workflow') ||
              msg.content.toLowerCase().includes('which workflow would you like to assign') ||
              msg.content.toLowerCase().includes('available workflows') ||
              msg.content.toLowerCase().includes('here are your available workflows') ||
              msg.content.toLowerCase().includes('just type the workflow name')
            ) &&
            (parsed.reasoning &&
             parsed.reasoning.toLowerCase().includes('job') &&
             (parsed.reasoning.toLowerCase().includes('workflow') || parsed.reasoning.toLowerCase().includes('assign')))
          );

          let finalIntent: string;

          if (isInJobWorkflowAssignment && intent === 'workflow') {
            finalIntent = 'job';
          } else if (parsed.confidence >= 0.8 && validIntents.includes(intent)) {
            finalIntent = intent;
          } else {
            // For low confidence, check if user input contains explicit intent keywords
            const input = state.userInput.toLowerCase();
            if (input.includes('job') || input.includes('create job') || input.includes('post job')) {
              finalIntent = 'job';
            } else if (input.includes('workflow') || input.includes('create workflow')) {
              finalIntent = 'workflow';
            } else if (input.includes('assessment') || input.includes('create assessment')) {
              finalIntent = 'assessment';
            } else if (state.conversationStage?.includes('assessment')) {
              finalIntent = 'assessment';
            } else if (state.conversationStage?.includes('workflow')) {
              finalIntent = 'workflow';
            } else if (state.conversationStage?.includes('job')) {
              finalIntent = 'job';
            } else {
              finalIntent = state.intent || state.previousIntent || 'job';
            }
          }

          let conversationStage = parsed.conversationStage || 'initial';

          if (parsed.confidence >= 0.8 && validIntents.includes(intent)) {
            if (finalIntent === 'workflow') {
              if (action === 'assignment' || state.userInput.toLowerCase().includes('attach workflow') ||
                  state.userInput.toLowerCase().includes('assign workflow')) {
                conversationStage = 'workflow_selection';
              } else if (action === 'editing' || action === 'modification') {
                conversationStage = 'workflow_editing';
              } else {
                conversationStage = 'workflow_creation';
              }
            } else if (finalIntent === 'job') {
              if (action === 'assignment' || state.userInput.toLowerCase().includes('attach workflow') ||
                  state.userInput.toLowerCase().includes('assign workflow')) {
                conversationStage = 'workflow_selection';
              } else if (action === 'editing' || action === 'modification') {
                conversationStage = 'job_editing';
              } else if (action === 'publishing') {
                conversationStage = 'job_publishing';
              } else {
                conversationStage = 'job_creation';
              }
            } else if (finalIntent === 'assessment') {
              if (action === 'editing' || action === 'modification') {
                conversationStage = 'assessment_editing';
              } else {
                conversationStage = 'assessment_creation';
              }
            }
          }

          return {
            ...state,
            intent: finalIntent,
            previousIntent: finalIntent,
            conversationStage,
          };

        } catch (err) {
          console.warn('[IntentClassifier] Failed to parse LLM response:', content);
          console.warn('[IntentClassifier] Parse error:', err);

          const input = state.userInput.toLowerCase();

          if (detectChatReset(state.userInput)) {
            const resetTimestamp = await storeResetMarker(qdrantClient, state.sessionId, state.userId, state.companyId);
            return {
              ...state,
              intent: 'reset',
              previousIntent: undefined,
              conversationStage: 'initial',
              isChatReset: true,
              resetTimestamp,
              chatResponse: 'Chat has been reset. How can I help you today?',
            };
          }

          const workflowKeywords = {
            creation: ['create workflow', 'new workflow', 'workflow creation', 'make workflow', 'build workflow'],
            assignment: ['assign workflow', 'attach workflow', 'workflow assignment'],
            editing: ['edit workflow', 'modify workflow', 'change workflow', 'update workflow']
          };

          const jobKeywords = {
            creation: ['create job', 'post job', 'new job', 'job posting', 'job creation', 'make job', 'add job'],
            editing: ['edit job', 'modify job', 'change job', 'update job', 'job description'],
            publishing: ['publish job', 'save job', 'submit job']
          };

          const assessmentKeywords = {
            creation: ['create assessment', 'new assessment', 'assessment creation', 'make assessment'],
            takeHome: ['take-home task', 'take home assessment', 'coding assignment'],
            liveTask: ['live coding', 'live assessment', 'live task', 'real-time coding'],
            domain: ['domain assessment', 'knowledge test', 'theoretical assessment']
          };

          let fallbackIntent: string;
          if (state.conversationStage?.startsWith('collect_')) {
            // Configuration collection stages should stay in workflow
            fallbackIntent = 'workflow';
          } else if (state.conversationStage?.includes('assessment')) {
            fallbackIntent = 'assessment';
          } else if (state.conversationStage?.includes('workflow')) {
            fallbackIntent = 'workflow';
          } else if (state.conversationStage?.includes('job')) {
            fallbackIntent = 'job';
          } else {
            fallbackIntent = state.intent || state.previousIntent || 'job';
          }

          let fallbackStage = 'initial'; // Reset stage to initial, will be set based on detected intent
          let detectedAction = 'creation';

          const allWorkflowKeywords = [...workflowKeywords.creation, ...workflowKeywords.assignment, ...workflowKeywords.editing];

          const isInJobWorkflowAssignment = (
            // Only consider it job workflow assignment if:
            // 1. We're specifically in workflow_selection stage AND
            // 2. The input doesn't contain explicit workflow creation keywords AND
            // 3. Recent conversation shows workflow assignment context
            state.conversationStage === 'workflow_selection' &&
            !workflowKeywords.creation.some((keyword: string) => input.includes(keyword)) &&
            conversationHistory.some(msg =>
              msg.content.toLowerCase().includes('would you like to assign a workflow') ||
              msg.content.toLowerCase().includes('which workflow would you like to assign') ||
              msg.content.toLowerCase().includes('available workflows') ||
              msg.content.toLowerCase().includes('here are your available workflows') ||
              msg.content.toLowerCase().includes('just type the workflow name')
            )
          );

          if (allWorkflowKeywords.some((keyword: string) => input.includes(keyword)) && !isInJobWorkflowAssignment) {
            fallbackIntent = 'workflow';
            if (workflowKeywords.assignment.some((keyword: string) => input.includes(keyword))) {
              fallbackStage = 'workflow_selection';
              detectedAction = 'assignment';
            } else if (workflowKeywords.editing.some((keyword: string) => input.includes(keyword))) {
              fallbackStage = 'workflow_editing';
              detectedAction = 'editing';
            } else {
              fallbackStage = 'workflow_creation';
              detectedAction = 'creation';
            }
          }
          else {
            const allJobKeywords = [...jobKeywords.creation, ...jobKeywords.editing, ...jobKeywords.publishing];
            if (allJobKeywords.some((keyword: string) => input.includes(keyword))) {
              fallbackIntent = 'job';
              if (jobKeywords.editing.some((keyword: string) => input.includes(keyword))) {
                fallbackStage = 'job_editing';
                detectedAction = 'editing';
              } else if (jobKeywords.publishing.some((keyword: string) => input.includes(keyword))) {
                fallbackStage = 'job_publishing';
                detectedAction = 'publishing';
              } else {
                fallbackStage = 'job_creation';
                detectedAction = 'creation';
              }
            }
            else {
              const allAssessmentKeywords = [...assessmentKeywords.creation, ...assessmentKeywords.takeHome, ...assessmentKeywords.liveTask, ...assessmentKeywords.domain];
              if (allAssessmentKeywords.some((keyword: string) => input.includes(keyword))) {
                fallbackIntent = 'assessment';
                fallbackStage = 'assessment_creation';
                detectedAction = 'creation';
              } else {
                // If no specific keywords detected, preserve current stage only if it matches the fallback intent
                if (fallbackIntent === 'job' && state.conversationStage?.includes('job')) {
                  fallbackStage = state.conversationStage;
                } else if (fallbackIntent === 'workflow' && state.conversationStage?.includes('workflow')) {
                  fallbackStage = state.conversationStage;
                } else if (fallbackIntent === 'assessment' && state.conversationStage?.includes('assessment')) {
                  fallbackStage = state.conversationStage;
                } else {
                  // Default stages based on intent
                  if (fallbackIntent === 'job') {
                    fallbackStage = 'job_creation';
                  } else if (fallbackIntent === 'workflow') {
                    fallbackStage = 'workflow_creation';
                  } else if (fallbackIntent === 'assessment') {
                    fallbackStage = 'assessment_creation';
                  }
                }
              }
            }
          }

          return {
            ...state,
            intent: fallbackIntent,
            previousIntent: fallbackIntent,
            conversationStage: fallbackStage,
          };
        }
      }
    } catch (error) {
      console.error('[IntentClassifier] Error during LLM intent classification:', error);

      const input = state.userInput.toLowerCase();

      if (detectChatReset(state.userInput)) {
        return {
          ...state,
          intent: 'reset',
          previousIntent: undefined,
          conversationStage: 'initial',
          isChatReset: true,
          resetTimestamp: new Date().toISOString(),
          chatResponse: 'Chat has been reset. How can I help you today?',
        };
      }

      let emergencyIntent: string;
      if (state.conversationStage?.includes('assessment')) {
        emergencyIntent = 'assessment';
      } else if (state.conversationStage?.includes('workflow')) {
        emergencyIntent = 'workflow';
      } else if (state.conversationStage?.includes('job')) {
        emergencyIntent = 'job';
      } else {
        emergencyIntent = state.intent || state.previousIntent || 'job';
      }

      let emergencyStage = 'initial'; // Reset to initial, will be set based on detected keywords

      if (input.includes('workflow')) {
        emergencyIntent = 'workflow';
        emergencyStage = 'workflow_creation';
      } else if (input.includes('assessment')) {
        emergencyIntent = 'assessment';
        emergencyStage = 'assessment_creation';
      } else if (input.includes('job')) {
        emergencyIntent = 'job';
        emergencyStage = 'job_creation';
      } else {
        // If no specific keywords, set default stage based on emergency intent
        if (emergencyIntent === 'job') {
          emergencyStage = 'job_creation';
        } else if (emergencyIntent === 'workflow') {
          emergencyStage = 'workflow_creation';
        } else if (emergencyIntent === 'assessment') {
          emergencyStage = 'assessment_creation';
        } else {
          emergencyStage = state.conversationStage || 'initial';
        }
      }


      return {
        ...state,
        intent: emergencyIntent,
        previousIntent: emergencyIntent,
        conversationStage: emergencyStage,
      };
    }
  };
}