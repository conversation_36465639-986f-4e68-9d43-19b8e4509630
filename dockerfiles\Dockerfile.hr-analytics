# Stage 1: Build
# Changes by <PERSON>hruv patel 

# FROM node:18-bullseye-slim AS build-stage
FROM public.ecr.aws/docker/library/node:20.19.3-bullseye-slim AS build-stage

# Install dependencies using apt-get (for Debian-based images like bullseye)
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    build-essential
 
# Set Python for node-gyp 
ENV PYTHON /usr/bin/python3

WORKDIR /app

COPY . .

RUN yarn

RUN yarn run build-hr

# Stage 2: Production
# Changes by Dhruv
# FROM node:18-bullseye-slim
FROM public.ecr.aws/docker/library/node:20.19.3-bullseye-slim

WORKDIR /app

# Copy the built application from the build container
COPY --from=build-stage /app/dist/apps/hr-analytics-api .

RUN yarn --production

EXPOSE 3003

CMD ["node", "main.js"]
